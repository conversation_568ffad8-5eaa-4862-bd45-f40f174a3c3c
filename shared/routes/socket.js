const express = require('express');
const router = express.Router();

// Socket.io management routes
let socketServer = null;

// Initialize socket server reference
const initializeSocketRoutes = (server) => {
  socketServer = server;
};

/**
 * @swagger
 * /api/v1/socket/status:
 *   get:
 *     summary: Get Socket.io server status
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: Socket.io server status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 connectedUsers:
 *                   type: integer
 *                 rooms:
 *                   type: integer
 *                 users:
 *                   type: array
 */
router.get('/status', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      status: 'unavailable',
      message: 'Socket.io server not initialized'
    });
  }

  const connectedUsers = socketServer.getConnectedUsers();
  const rooms = Array.from(socketServer.rooms.keys());

  res.json({
    status: 'active',
    connectedUsers: connectedUsers.length,
    totalRooms: rooms.length,
    users: connectedUsers,
    rooms: rooms,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/broadcast:
 *   post:
 *     summary: Broadcast message to all connected users
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               event:
 *                 type: string
 *               message:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Message broadcasted successfully
 */
router.post('/broadcast', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { event, message, data } = req.body;

  if (!event || !message) {
    return res.status(400).json({
      success: false,
      message: 'Event and message are required'
    });
  }

  const broadcastData = {
    message,
    data: data || {},
    timestamp: new Date().toISOString(),
    from: 'system'
  };

  socketServer.broadcastToAll(event, broadcastData);

  res.json({
    success: true,
    message: 'Message broadcasted successfully',
    event,
    recipients: socketServer.getConnectedUsers().length
  });
});

/**
 * @swagger
 * /api/v1/socket/notify:
 *   post:
 *     summary: Send notification to specific user or role
 *     tags: [Socket.io]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *               role:
 *                 type: string
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high, urgent]
 *     responses:
 *       200:
 *         description: Notification sent successfully
 */
router.post('/notify', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { userId, role, type, title, message, priority } = req.body;

  if (!title || !message) {
    return res.status(400).json({
      success: false,
      message: 'Title and message are required'
    });
  }

  const notification = {
    id: Date.now(),
    type: type || 'info',
    title,
    message,
    priority: priority || 'medium',
    timestamp: new Date().toISOString(),
    from: 'system'
  };

  let sent = false;
  let recipients = 0;

  if (userId) {
    sent = socketServer.sendToUser(userId, 'notification:received', notification);
    recipients = sent ? 1 : 0;
  } else if (role) {
    socketServer.sendToRole(role, 'notification:received', notification);
    sent = true;
    recipients = socketServer.getConnectedUsers().filter(user => user.role === role).length;
  } else {
    socketServer.broadcastToAll('notification:received', notification);
    sent = true;
    recipients = socketServer.getConnectedUsers().length;
  }

  res.json({
    success: sent,
    message: sent ? 'Notification sent successfully' : 'User not found or offline',
    notification,
    recipients
  });
});

/**
 * @swagger
 * /api/v1/socket/analytics:
 *   post:
 *     summary: Send analytics event
 *     tags: [Socket.io]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               page:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Analytics event sent successfully
 */
router.post('/analytics', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { type, page, data } = req.body;

  const analyticsEvent = {
    type: type || 'custom',
    page,
    data: data || {},
    timestamp: new Date().toISOString(),
    source: 'api'
  };

  // Send to admin dashboard
  socketServer.io.to('admin').emit('analytics:api_event', analyticsEvent);

  res.json({
    success: true,
    message: 'Analytics event sent successfully',
    event: analyticsEvent
  });
});

/**
 * @swagger
 * /api/v1/socket/rooms:
 *   get:
 *     summary: Get all active rooms
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: List of active rooms
 */
router.get('/rooms', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const rooms = Array.from(socketServer.rooms.entries()).map(([roomId, socketIds]) => ({
    roomId,
    userCount: socketIds.size,
    users: Array.from(socketIds).map(socketId => socketServer.userSockets.get(socketId)).filter(Boolean)
  }));

  res.json({
    success: true,
    rooms,
    totalRooms: rooms.length,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/users/online:
 *   get:
 *     summary: Get all online users
 *     tags: [Socket.io]
 *     responses:
 *       200:
 *         description: List of online users
 */
router.get('/users/online', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const onlineUsers = socketServer.getConnectedUsers();

  res.json({
    success: true,
    users: onlineUsers,
    count: onlineUsers.length,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /api/v1/socket/test:
 *   post:
 *     summary: Test Socket.io connection
 *     tags: [Socket.io]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Test message sent
 */
router.post('/test', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  const { message } = req.body;

  const testData = {
    message: message || 'Test message from API',
    timestamp: new Date().toISOString(),
    source: 'api_test'
  };

  socketServer.broadcastToAll('test:message', testData);

  res.json({
    success: true,
    message: 'Test message sent to all connected users',
    data: testData,
    recipients: socketServer.getConnectedUsers().length
  });
});

/**
 * @swagger
 * /api/v1/socket/temp-token:
 *   post:
 *     summary: Generate temporary token for Socket.io
 *     tags: [Socket.io]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceFingerprint:
 *                 type: string
 *                 description: Device fingerprint for security
 *     responses:
 *       200:
 *         description: Temporary token generated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 token:
 *                   type: string
 *                 expiresIn:
 *                   type: string
 *                 user:
 *                   type: object
 */
router.post('/temp-token', (req, res) => {
  if (!socketServer) {
    return res.status(503).json({
      success: false,
      message: 'Socket.io server not initialized'
    });
  }

  try {
    const { deviceFingerprint } = req.body || {};

    // Generate temporary token
    const tempToken = socketServer.generateTemporaryToken();
    const tempUser = socketServer.createTemporaryUser(tempToken);

    // Add device fingerprint if provided
    if (deviceFingerprint) {
      tempUser.deviceFingerprint = deviceFingerprint;
    }

    res.json({
      success: true,
      message: 'Temporary token generated successfully',
      token: tempToken,
      expiresIn: '1h',
      user: {
        id: tempUser.id,
        name: tempUser.name,
        role: tempUser.role,
        isTemporary: true
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error generating temporary token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate temporary token',
      error: error.message
    });
  }
});

module.exports = { router, initializeSocketRoutes };
