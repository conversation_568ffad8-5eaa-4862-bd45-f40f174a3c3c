const { Server } = require('socket.io')
const jwt = require('jsonwebtoken')

class SocketServer {
  constructor(httpServer) {
    this.io = new Server(httpServer, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:5173",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.connectedUsers = new Map() // userId -> socket.id
    this.userSockets = new Map()   // socket.id -> user info
    this.rooms = new Map()         // room -> Set of socket.ids

    this.setupMiddleware()
    this.setupEventHandlers()
  }

  setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
        
        if (!token) {
          return next(new Error('Authentication error: No token provided'))
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET)
        const user = await this.getUserById(decoded.userId)
        
        if (!user) {
          return next(new Error('Authentication error: User not found'))
        }

        socket.userId = user.id
        socket.userRole = user.role
        socket.userData = {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name
        }

        next()
      } catch (error) {
        console.error('Socket authentication error:', error)
        next(new Error('Authentication error: Invalid token'))
      }
    })
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User ${socket.userData.email} connected (${socket.id})`)
      
      // Store user connection
      this.connectedUsers.set(socket.userId, socket.id)
      this.userSockets.set(socket.id, socket.userData)

      // Join user to their personal room
      socket.join(`user:${socket.userId}`)
      
      // Join role-based rooms
      socket.join(`role:${socket.userRole}`)
      
      // Admin users join admin room
      if (socket.userRole === 'admin') {
        socket.join('admin')
      }

      // Send connection confirmation
      socket.emit('connected', {
        message: 'Connected to HLenergy real-time server',
        userId: socket.userId,
        timestamp: new Date().toISOString()
      })

      // Broadcast user online status to admins
      socket.to('admin').emit('user:online', {
        userId: socket.userId,
        userData: socket.userData,
        timestamp: new Date().toISOString()
      })

      // Handle real-time analytics tracking
      socket.on('analytics:track', (data) => {
        this.handleAnalyticsEvent(socket, data)
      })

      // Handle customer communication events
      socket.on('communication:send', (data) => {
        this.handleCommunication(socket, data)
      })

      // Handle project updates
      socket.on('project:update', (data) => {
        this.handleProjectUpdate(socket, data)
      })

      // Handle notification events
      socket.on('notification:read', (data) => {
        this.handleNotificationRead(socket, data)
      })

      // Handle typing indicators
      socket.on('typing:start', (data) => {
        this.handleTypingStart(socket, data)
      })

      socket.on('typing:stop', (data) => {
        this.handleTypingStop(socket, data)
      })

      // Handle room joining/leaving
      socket.on('room:join', (roomId) => {
        this.joinRoom(socket, roomId)
      })

      socket.on('room:leave', (roomId) => {
        this.leaveRoom(socket, roomId)
      })

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        console.log(`User ${socket.userData.email} disconnected: ${reason}`)
        
        // Remove from tracking
        this.connectedUsers.delete(socket.userId)
        this.userSockets.delete(socket.id)

        // Broadcast user offline status to admins
        socket.to('admin').emit('user:offline', {
          userId: socket.userId,
          userData: socket.userData,
          reason,
          timestamp: new Date().toISOString()
        })

        // Clean up rooms
        this.cleanupUserRooms(socket)
      })
    })
  }

  // Analytics event handling
  handleAnalyticsEvent(socket, data) {
    const event = {
      ...data,
      userId: socket.userId,
      timestamp: new Date().toISOString(),
      sessionId: socket.id
    }

    // Broadcast to admin dashboard for real-time analytics
    this.io.to('admin').emit('analytics:event', event)

    // Store in database (you can implement this)
    this.storeAnalyticsEvent(event)
  }

  // Communication handling
  handleCommunication(socket, data) {
    const communication = {
      ...data,
      senderId: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Send to specific user if specified
    if (data.recipientId) {
      this.sendToUser(data.recipientId, 'communication:received', communication)
    }

    // Broadcast to admins
    this.io.to('admin').emit('communication:new', communication)

    // Store in database
    this.storeCommunication(communication)
  }

  // Project update handling
  handleProjectUpdate(socket, data) {
    const update = {
      ...data,
      updatedBy: socket.userId,
      timestamp: new Date().toISOString()
    }

    // Notify project stakeholders
    if (data.projectId) {
      this.io.to(`project:${data.projectId}`).emit('project:updated', update)
    }

    // Notify admins
    this.io.to('admin').emit('project:update', update)
  }

  // Notification read handling
  handleNotificationRead(socket, data) {
    // Mark notification as read in database
    this.markNotificationAsRead(data.notificationId, socket.userId)

    // Update notification count for user
    this.sendNotificationCount(socket.userId)
  }

  // Typing indicators
  handleTypingStart(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_started', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  handleTypingStop(socket, data) {
    if (data.roomId) {
      socket.to(data.roomId).emit('typing:user_stopped', {
        userId: socket.userId,
        userData: socket.userData
      })
    }
  }

  // Room management
  joinRoom(socket, roomId) {
    socket.join(roomId)
    
    if (!this.rooms.has(roomId)) {
      this.rooms.set(roomId, new Set())
    }
    this.rooms.get(roomId).add(socket.id)

    socket.emit('room:joined', { roomId })
    socket.to(roomId).emit('room:user_joined', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  leaveRoom(socket, roomId) {
    socket.leave(roomId)
    
    if (this.rooms.has(roomId)) {
      this.rooms.get(roomId).delete(socket.id)
      if (this.rooms.get(roomId).size === 0) {
        this.rooms.delete(roomId)
      }
    }

    socket.emit('room:left', { roomId })
    socket.to(roomId).emit('room:user_left', {
      userId: socket.userId,
      userData: socket.userData
    })
  }

  cleanupUserRooms(socket) {
    for (const [roomId, socketIds] of this.rooms.entries()) {
      if (socketIds.has(socket.id)) {
        socketIds.delete(socket.id)
        if (socketIds.size === 0) {
          this.rooms.delete(roomId)
        }
      }
    }
  }

  // Utility methods
  sendToUser(userId, event, data) {
    const socketId = this.connectedUsers.get(userId)
    if (socketId) {
      this.io.to(socketId).emit(event, data)
      return true
    }
    return false
  }

  sendToRole(role, event, data) {
    this.io.to(`role:${role}`).emit(event, data)
  }

  broadcastToAll(event, data) {
    this.io.emit(event, data)
  }

  getConnectedUsers() {
    return Array.from(this.userSockets.values())
  }

  isUserOnline(userId) {
    return this.connectedUsers.has(userId)
  }

  // Database operations (implement these based on your database)
  async getUserById(userId) {
    // Implement user lookup
    // For now, return a mock user
    return {
      id: userId,
      email: '<EMAIL>',
      role: 'user',
      name: 'User Name'
    }
  }

  async storeAnalyticsEvent(event) {
    // Implement analytics storage
    console.log('Analytics event:', event)
  }

  async storeCommunication(communication) {
    // Implement communication storage
    console.log('Communication:', communication)
  }

  async markNotificationAsRead(notificationId, userId) {
    // Implement notification read marking
    console.log('Notification read:', notificationId, userId)
  }

  async sendNotificationCount(userId) {
    // Get unread notification count and send to user
    const count = 0 // Implement count logic
    this.sendToUser(userId, 'notifications:count', { count })
  }
}

module.exports = SocketServer
